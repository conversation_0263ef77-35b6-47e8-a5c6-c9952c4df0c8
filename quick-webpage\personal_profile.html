﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人介绍 - 张三</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 50px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 2px solid #eee;
        }
        .avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: #74b9ff;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
        }
        .name {
            font-size: 2.5em;
            color: #2d3436;
            margin-bottom: 10px;
        }
        .title {
            font-size: 1.2em;
            color: #636e72;
        }
        .section {
            padding: 30px 0;
            border-bottom: 1px solid #eee;
        }
        .section:last-child {
            border-bottom: none;
        }
        .section h2 {
            color: #2d3436;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .skill {
            background: #74b9ff;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .contact {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
        }
        .contact-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            flex: 1;
            min-width: 200px;
        }
        .contact-item i {
            font-size: 2em;
            color: #74b9ff;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="avatar">张</div>
            <h1 class="name">张三</h1>
            <p class="title">全栈开发工程师</p>
        </div>

        <div class="section">
            <h2>关于我</h2>
            <p>我是一名热爱技术的全栈开发工程师，拥有5年的Web开发经验。专注于前端和后端技术，喜欢学习新技术并将其应用到实际项目中。</p>
        </div>

        <div class="section">
            <h2>技能专长</h2>
            <div class="skills">
                <span class="skill">JavaScript</span>
                <span class="skill">Python</span>
                <span class="skill">React</span>
                <span class="skill">Vue.js</span>
                <span class="skill">Node.js</span>
                <span class="skill">MySQL</span>
                <span class="skill">MongoDB</span>
                <span class="skill">Docker</span>
                <span class="skill">AWS</span>
                <span class="skill">Git</span>
            </div>
        </div>

        <div class="section">
            <h2>工作经历</h2>
            <div style="margin-bottom: 20px;">
                <h3>高级前端开发工程师 - ABC科技公司 (2021-至今)</h3>
                <p>负责公司主要产品的前端开发，使用React和Vue.js构建用户界面，优化用户体验。</p>
            </div>
            <div>
                <h3>全栈开发工程师 - XYZ创业公司 (2019-2021)</h3>
                <p>独立负责整个Web应用的开发，从前端到后端，从设计到部署。</p>
            </div>
        </div>

        <div class="section">
            <h2>联系方式</h2>
            <div class="contact">
                <div class="contact-item">
                    <div style="font-size: 2em; color: #74b9ff; margin-bottom: 10px;">📧</div>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <div style="font-size: 2em; color: #74b9ff; margin-bottom: 10px;">📱</div>
                    <p>138-0000-0000</p>
                </div>
                <div class="contact-item">
                    <div style="font-size: 2em; color: #74b9ff; margin-bottom: 10px;">🌐</div>
                    <p>github.com/zhangsan</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.querySelectorAll(".skill").forEach(skill => {
            skill.addEventListener("mouseenter", function() {
                this.style.transform = "scale(1.1)";
                this.style.transition = "transform 0.3s";
            });

            skill.addEventListener("mouseleave", function() {
                this.style.transform = "scale(1)";
            });
        });

        // 页面加载动画
        window.addEventListener("load", function() {
            document.querySelector(".container").style.opacity = "0";
            document.querySelector(".container").style.transform = "translateY(50px)";
            document.querySelector(".container").style.transition = "all 0.8s ease";

            setTimeout(() => {
                document.querySelector(".container").style.opacity = "1";
                document.querySelector(".container").style.transform = "translateY(0)";
            }, 100);
        });
    </script>
</body>
</html>