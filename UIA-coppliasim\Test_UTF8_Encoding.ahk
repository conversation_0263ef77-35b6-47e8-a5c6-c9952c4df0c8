#Requires AutoHotkey v2

/*
=============================================================================
UTF-8编码测试脚本
=============================================================================
测试中文字符的正确编码和显示
=============================================================================
*/

; 测试文件名
testFile := "UTF8_Test_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"

; 测试内容（包含各种中文字符）
testContent := "=== UTF-8编码测试 ===`n"
testContent .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n"
testContent .= "测试内容:`n"
testContent .= "• 监控系统初始化成功`n"
testContent .= "• 鼠标点击操作记录`n"
testContent .= "• 仿真控制：开始/停止/暂停`n"
testContent .= "• 脚本代码：sim.startSimulation()`n"
testContent .= "• 详细信息：类型、位置、模式`n"
testContent .= "• 特殊字符：①②③④⑤⑥⑦⑧⑨⑩`n"
testContent .= "• 符号测试：→←↑↓★☆♠♥♦♣`n`n"

; 模拟监控日志格式
testContent .= "=== 模拟监控日志 ===`n"
testContent .= "时间: " . FormatTime(A_Now, "HH:mm:ss") . "`n"
testContent .= "操作: 鼠标点击`n"
testContent .= "元素: [工具按钮]`n"
testContent .= "脚本: sim.startSimulation() -- 开始仿真`n"
testContent .= "详情: 类型:按钮 | 类:ToolButton | ID:startSimulation | 位置:(269,49)`n"
testContent .= "---`n"

try {
    ; 写入UTF-8编码文件
    FileAppend(testContent, testFile, "UTF-8")
    
    ; 读取并验证
    readContent := FileRead(testFile, "UTF-8")
    
    if readContent = testContent {
        MsgBox("✅ UTF-8编码测试成功！`n`n测试文件: " . testFile . "`n`n中文字符应该能正确显示。`n请用文本编辑器打开测试文件验证。", "编码测试结果")
    } else {
        MsgBox("❌ UTF-8编码测试失败！`n`n读取的内容与写入的内容不匹配。", "编码测试结果")
    }
    
} catch Error as e {
    MsgBox("测试失败: " . e.Message)
}

ExitApp
