﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品展示 - 智能手表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f6fa;
            color: #2f3542;
        }
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        .hero h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .hero p {
            font-size: 1.3em;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .features {
            padding: 80px 0;
            background: white;
        }
        .features h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2f3542;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }
        .feature-card {
            text-align: center;
            padding: 40px 20px;
            border-radius: 15px;
            background: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }
        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #2f3542;
        }
        .specs {
            padding: 80px 0;
            background: #f8f9fa;
        }
        .specs h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2f3542;
        }
        .spec-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .spec-row {
            display: flex;
            border-bottom: 1px solid #eee;
        }
        .spec-row:last-child {
            border-bottom: none;
        }
        .spec-label {
            flex: 1;
            padding: 20px;
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .spec-value {
            flex: 2;
            padding: 20px;
        }
        .price {
            text-align: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        .price h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .price-value {
            font-size: 4em;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="hero">
        <div class="container">
            <h1>智能手表 Pro</h1>
            <p>科技与时尚的完美结合，让生活更智能</p>
            <a href="#features" class="btn">了解更多</a>
        </div>
    </div>

    <div class="features" id="features">
        <div class="container">
            <h2>产品特色</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">⌚</div>
                    <h3>精准计时</h3>
                    <p>瑞士机芯，精确到秒，永不掉队的时间伙伴</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💓</div>
                    <h3>健康监测</h3>
                    <p>24小时心率监测，睡眠分析，让健康看得见</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏃</div>
                    <h3>运动追踪</h3>
                    <p>多种运动模式，GPS定位，专业运动数据分析</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💧</div>
                    <h3>防水设计</h3>
                    <p>50米防水，游泳、洗澡都不怕，全天候陪伴</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔋</div>
                    <h3>长续航</h3>
                    <p>7天超长续航，磁吸充电，告别频繁充电烦恼</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>智能连接</h3>
                    <p>蓝牙5.0，消息提醒，通话功能，手腕上的智能助手</p>
                </div>
            </div>
        </div>
    </div>

    <div class="specs">
        <div class="container">
            <h2>技术规格</h2>
            <div class="spec-table">
                <div class="spec-row">
                    <div class="spec-label">显示屏</div>
                    <div class="spec-value">1.4英寸 AMOLED 高清彩屏</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">处理器</div>
                    <div class="spec-value">双核 ARM Cortex-M4</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">内存</div>
                    <div class="spec-value">512MB RAM + 4GB 存储</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">电池</div>
                    <div class="spec-value">420mAh 锂电池，7天续航</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">连接</div>
                    <div class="spec-value">蓝牙 5.0, Wi-Fi, GPS</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">传感器</div>
                    <div class="spec-value">心率、加速度、陀螺仪、气压</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">防水等级</div>
                    <div class="spec-value">5ATM (50米防水)</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">重量</div>
                    <div class="spec-value">45g (不含表带)</div>
                </div>
            </div>
        </div>
    </div>

    <div class="price">
        <div class="container">
            <h2>特惠价格</h2>
            <div class="price-value">¥1,299</div>
            <a href="#" class="btn" onclick="alert('感谢您的关注！请联系客服购买。')">立即购买</a>
        </div>
    </div>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有特色卡片
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>