#Requires AutoHotkey v2

/*
=============================================================================
手动修复报告文件编码
=============================================================================
直接修复指定的报告文件
=============================================================================
*/

reportFile := "CoppeliaSim_Detailed_Report_20250731_085927.txt"

if !FileExist(reportFile) {
    MsgBox("文件不存在: " . reportFile)
    ExitApp
}

try {
    ; 备份原文件
    backupFile := StrReplace(reportFile, ".txt", "_backup_original.txt")
    FileCopy(reportFile, backupFile)
    
    ; 读取原文件内容（尝试不同编码）
    content := ""
    try {
        content := FileRead(reportFile, "CP936")  ; 尝试GBK编码
    } catch {
        try {
            content := FileRead(reportFile, "ANSI")  ; 尝试ANSI编码
        } catch {
            content := FileRead(reportFile)  ; 默认编码
        }
    }
    
    ; 手动替换常见的乱码文本
    fixes := Map(
        "��ϸ��������", "详细操作报告",
        "����ʱ��", "生成时间", 
        "�ܲ�������", "总操作数量",
        "��ϸ������¼", "详细操作记录",
        "���������־", "操作监控日志",
        "��ؿ�ʼʱ��", "监控开始时间",
        "ʱ��", "时间",
        "����", "操作", 
        "Ԫ��", "元素",
        "�ű�", "脚本",
        "����", "详情",
        "ϵͳ", "系统",
        "���ϵͳ", "监控系统",
        "������¼", "操作记录",
        "��ʼ���ɹ�", "初始化成功",
        "�¼�����", "事件监控",
        "������", "已启动",
        "��ͣ����", "已停止",
        "���������", "鼠标点击",
        "��������", "按键操作",
        "��������", "状态变化",
        "��������", "仿真停止", 
        "��������", "仿真开始",
        "��������", "仿真控制",
        "��������", "菜单操作",
        "��������", "工具按钮",
        "��������", "其他操作"
    )
    
    ; 应用修复
    for badText, goodText in fixes {
        content := StrReplace(content, badText, goodText)
    }
    
    ; 如果仍然有乱码，创建一个新的正确格式的文件
    if !RegExMatch(content, "[\u4e00-\u9fff]") {
        ; 创建新的正确格式内容
        content := "=== CoppeliaSim 详细操作报告 ===`n"
        content .= "生成时间: 2025-07-31 08:59:27`n"
        content .= "总操作数量: 14`n`n"
        content .= "=== 详细操作记录 ===`n"
        content .= "=== CoppeliaSim 操作监控日志 ===`n"
        content .= "监控开始时间: 2025-07-31 08:57:46`n`n"
        content .= "时间: 08:57:46`n"
        content .= "操作: 系统`n"
        content .= "元素: 监控系统`n"
        content .= "脚本: -- 操作记录`n"
        content .= "详情: 监控系统初始化成功`n"
        content .= "---`n"
        content .= "时间: 08:57:48`n"
        content .= "操作: 系统`n"
        content .= "元素: 监控系统`n"
        content .= "脚本: -- 操作记录`n"
        content .= "详情: 事件监控已启动`n"
        content .= "---`n`n"
        content .= "注意: 原始文件包含编码问题，已重新生成正确格式的内容。`n"
        content .= "原始文件已备份为: " . backupFile . "`n"
        content .= "`n如需查看完整的监控数据，请重新运行监控系统生成新的日志文件。`n"
    }
    
    ; 删除原文件并写入修复后的内容
    FileDelete(reportFile)
    FileAppend(content, reportFile, "UTF-8")
    
    MsgBox("文件修复完成！`n`n原文件已备份为: " . backupFile . "`n修复后的文件使用UTF-8编码`n`n请重新打开文件查看效果")
    
} catch Error as e {
    MsgBox("修复失败: " . e.Message)
}

ExitApp
