#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

; 调试脚本：显示 CoppeliaSim 的所有 UI 元素
; 使用方法：先启动 CoppeliaSim，然后运行此脚本

try {
    ; 查找 CoppeliaSim 窗口
    hwnd := WinExist("ahk_exe coppeliasim.exe")
    if !hwnd {
        MsgBox("请先启动 CoppeliaSim")
        ExitApp
    }
    
    ; 获取窗口元素
    element := UIA.ElementFromHandle(hwnd)
    
    ; 显示窗口的所有子元素信息
    MsgBox("正在分析 CoppeliaSim UI 结构，请稍等...")
    
    ; 获取所有元素的详细信息
    allElements := element.DumpAll()
    
    ; 将信息保存到文件 - 使用 UTF-8 编码
    FileDelete("CoppeliaSim_UI_Structure.txt")  ; 删除旧文件
    FileAppend(allElements, "CoppeliaSim_UI_Structure.txt", "UTF-8")
    
    MsgBox("UI 结构已保存到 CoppeliaSim_UI_Structure.txt 文件中`n请查看该文件找到正确的按钮名称")
    
} catch Error as e {
    MsgBox("错误: " . e.Message)
}
