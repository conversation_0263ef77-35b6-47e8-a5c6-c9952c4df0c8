# UIA-v2

This library is a wrapper for the UIAutomation framework, which can be used to automate windows that normally might be difficult or impossible to automate with AHK.

The main library file is UIA.ahk, which is based on [thqby's UIAutomation](https://github.com/thqby/ahk2_lib/tree/master/UIAutomation) library, but additionally contains custom helper functions for easier use of UIA. [Read here]() how this library differs from thqby's and the original UIAutomation framework by Microsoft.
Additionally there is UIA_Browser.ahk, which contains helper functions for browser automation (fetching URLs, switching tabs etc). Currently it supports Chrome, Edge, and Firefox.

## For more information and tutorials, [check out the Wiki](https://github.com/Descolada/UIA-v2/wiki).

### For instructional videos, check out [the Tutorial Series](https://www.youtube.com/playlist?list=PLg-VAp_I6_oTfdL9sUqcC7s61jcQW9K38), [<PERSON> the Automator](https://www.the-automator.com/automate-any-program-with-ui-automation/) and [Axlefublr](https://www.youtube.com/watch?v=o2E6sRxFoB0)

Multiple examples are included in the Examples folder.

If you have questions or issues, either post it in the [AutoHotkey forums' main thread](https://www.autohotkey.com/boards/viewtopic.php?f=83&t=113065) or create a [new issue](https://github.com/Descolada/UIA-v2/issues).

If you wish to support me in this and other projects:
[!["Buy Me A Coffee"](https://www.buymeacoffee.com/assets/img/custom_images/orange_img.png)](https://www.buymeacoffee.com/descolada)