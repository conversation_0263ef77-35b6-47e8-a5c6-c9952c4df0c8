﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片画廊 - 修复版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-info {
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
        }

        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.4);
        }

        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
        }

        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 20px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }

        .gallery-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .gallery-description {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 50px 0;
        }

        @media (max-width: 768px) {
            .gallery {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 精美图片画廊</h1>
            <p>Canvas生成的彩色艺术图片，完全离线可用</p>
        </div>

        <div class="status-info">
            ✅ 修复完成！现在使用Canvas生成的彩色占位图片，完全离线可用
        </div>

        <div class="loading" id="loading">
            🎨 正在生成精美图片...
        </div>

        <div class="gallery" id="gallery">
            <!-- 动态生成的图片将在这里显示 -->
        </div>
    </div>

    <script>
        var imageList = [
            {title: "抽象艺术 - 蓝色梦境", description: "流动的蓝色渐变，营造出梦幻般的视觉效果，让人沉浸在宁静的艺术氛围中。", colors: ["#4facfe", "#00f2fe", "#43e97b"]},
            {title: "几何图案 - 橙色活力", description: "充满活力的橙色几何图案，展现现代艺术的简约美学和动感节奏。", colors: ["#fa709a", "#fee140", "#ff6b6b"]},
            {title: "自然风光 - 绿色森林", description: "清新的绿色调，仿佛置身于茂密的森林中，感受大自然的生机与活力。", colors: ["#56ab2f", "#a8e6cf", "#88d8a3"]},
            {title: "城市夜景 - 紫色霓虹", description: "神秘的紫色霓虹效果，展现现代都市的繁华与科技感。", colors: ["#667eea", "#764ba2", "#f093fb"]},
            {title: "海洋波浪 - 青色深邃", description: "深邃的青色波浪纹理，如同海洋的深处，充满神秘和宁静。", colors: ["#4ecdc4", "#44a08d", "#096dd9"]},
            {title: "日落黄昏 - 金色温暖", description: "温暖的金色渐变，如同黄昏时分的夕阳，给人温馨舒适的感觉。", colors: ["#f7971e", "#ffd200", "#ffb347"]},
            {title: "星空银河 - 深蓝神秘", description: "深邃的星空效果，点点星光在深蓝色的背景中闪烁，充满宇宙的神秘感。", colors: ["#0c3483", "#a2a2d0", "#fbc2eb"]},
            {title: "春日花园 - 粉色浪漫", description: "柔美的粉色调，如同春日花园中盛开的樱花，浪漫而温柔。", colors: ["#ffecd2", "#fcb69f", "#ff9a9e"]},
            {title: "科技未来 - 银色金属", description: "现代科技感的银色金属质感，展现未来世界的简约与精致。", colors: ["#bdc3c7", "#2c3e50", "#74b9ff"]},
            {title: "热带风情 - 珊瑚橙", description: "充满热带风情的珊瑚橙色调，让人联想到温暖的海滩和夏日阳光。", colors: ["#ff7675", "#fd79a8", "#fdcb6e"]},
            {title: "极光幻境 - 绿色神秘", description: "如同北极光般的绿色幻境，神秘而美丽，充满大自然的奇迹。", colors: ["#00b894", "#00cec9", "#a29bfe"]},
            {title: "沙漠黄昏 - 暖色调", description: "沙漠黄昏的暖色调，金黄与橙红的完美融合，展现大自然的壮美。", colors: ["#fab1a0", "#e17055", "#fdcb6e"]}
        ];

        function generateCanvasImage(colors) {
            var canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            var ctx = canvas.getContext('2d');
            var gradient = ctx.createLinearGradient(0, 0, 400, 300);
            for (var i = 0; i < colors.length; i++) {
                gradient.addColorStop(i / (colors.length - 1), colors[i]);
            }
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            ctx.globalAlpha = 0.3;
            for (var j = 0; j < 20; j++) {
                ctx.beginPath();
                ctx.arc(Math.random() * 400, Math.random() * 300, Math.random() * 50 + 10, 0, Math.PI * 2);
                ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];
                ctx.fill();
            }
            return canvas.toDataURL('image/png');
        }

        function renderGallery() {
            var gallery = document.getElementById('gallery');
            var loading = document.getElementById('loading');
            loading.style.display = 'none';
            for (var i = 0; i < imageList.length; i++) {
                var image = imageList[i];
                var imageSrc = generateCanvasImage(image.colors);
                var item = document.createElement('div');
                item.className = 'gallery-item';
                var img = document.createElement('img');
                img.src = imageSrc;
                img.alt = image.title;
                var overlay = document.createElement('div');
                overlay.className = 'gallery-overlay';
                var title = document.createElement('div');
                title.className = 'gallery-title';
                title.textContent = image.title;
                var desc = document.createElement('div');
                desc.className = 'gallery-description';
                desc.textContent = image.description;
                overlay.appendChild(title);
                overlay.appendChild(desc);
                item.appendChild(img);
                item.appendChild(overlay);
                item.onclick = (function(img) {
                    return function() {
                        alert('点击了图片: ' + img.title + '\\n\\n' + img.description + '\\n\\n这是一个演示功能，展示了图片的交互效果。');
                    };
                })(image);
                gallery.appendChild(item);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(renderGallery, 500);
        });
    </script>
</body>
</html>