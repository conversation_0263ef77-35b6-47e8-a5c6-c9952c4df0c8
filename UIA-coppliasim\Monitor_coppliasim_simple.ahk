#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 实时操作监控系统
=============================================================================
功能：
1. 实时监控用户在 CoppeliaSim 中的所有操作行为
2. 记录点击事件、菜单操作、按钮操作等
3. 分析操作背后的脚本和代码逻辑
4. 生成操作日志和行为分析报告

使用方法：
1. 确保 CoppeliaSim 正在运行
2. 运行此脚本开始监控
3. 在 CoppeliaSim 中进行各种操作
4. 查看实时监控窗口和日志文件

热键：
F9  - 开始/停止监控
F10 - 显示监控统计
F11 - 清空监控日志
F12 - 退出监控程序
=============================================================================
*/

; 全局变量
global monitoringActive := false
global coppliaElement := ""
global logFile := "CoppeliaSim_Monitor_Log.txt"
global monitorGui := ""
global logListView := ""
global operationCount := 0
global lastOperation := ""
global eventHandlers := []

; 初始化监控系统
InitializeMonitor()

; 设置热键
F9::ToggleMonitoring()
F10::ShowStatistics()
F11::ClearLog()
F12::ExitMonitor()

; 显示主界面
ShowMainInterface()

return

; 初始化监控系统
InitializeMonitor() {
    global coppliaElement, logFile

    ; 检查 CoppeliaSim 是否运行
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("CoppeliaSim 未运行，是否启动？", "监控系统", "YesNo")
        if result = "Yes" {
            Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
            WinWaitActive("ahk_exe coppeliasim.exe", , 30)
            Sleep(3000)
        } else {
            ExitApp
        }
    }

    try {
        ; 获取 CoppeliaSim 窗口元素
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        coppliaElement := UIA.ElementFromHandle(hwnd)

        ; 初始化日志文件 - 使用 UTF-8 编码，添加错误处理
        try {
            FileDelete(logFile)
        } catch {
            ; 忽略删除失败（文件可能不存在）
        }

        try {
            FileAppend("=== CoppeliaSim 操作监控日志 ===`n", logFile, "UTF-8")
            FileAppend("监控开始时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", logFile, "UTF-8")
        } catch Error as e {
            MsgBox("警告：无法创建日志文件`n错误: " . e.Message . "`n监控功能仍可正常使用")
        }

        AddLog("监控系统初始化成功")

    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 显示主界面
ShowMainInterface() {
    global monitorGui, logListView

    ; 创建监控界面 - 添加最小尺寸限制
    monitorGui := Gui("+Resize +MinSize640x480", "CoppeliaSim 操作监控系统")
    monitorGui.SetFont("s9")

    ; 添加控件
    monitorGui.Add("Text", "x10 y10", "监控状态:")
    statusText := monitorGui.Add("Text", "x80 y10 w100 c0xFF0000", "未开始")
    statusText.Name := "StatusText"

    monitorGui.Add("Text", "x10 y35", "操作计数:")
    countText := monitorGui.Add("Text", "x80 y35 w100", "0")
    countText.Name := "CountText"

    ; 控制按钮
    startBtn := monitorGui.Add("Button", "x200 y10 w80 h25", "开始监控")
    startBtn.OnEvent("Click", (*) => ToggleMonitoring())
    startBtn.Name := "StartBtn"

    stopBtn := monitorGui.Add("Button", "x290 y10 w80 h25", "停止监控")
    stopBtn.OnEvent("Click", (*) => ToggleMonitoring())
    stopBtn.Name := "StopBtn"

    clearBtn := monitorGui.Add("Button", "x380 y10 w80 h25", "清空日志")
    clearBtn.OnEvent("Click", (*) => ClearLog())

    exportBtn := monitorGui.Add("Button", "x470 y10 w80 h25", "导出日志")
    exportBtn.OnEvent("Click", (*) => ExportDetailedLog())

    ; 日志显示区域 - 改进列标题
    monitorGui.Add("Text", "x10 y60", "操作日志:")
    logListView := monitorGui.Add("ListView", "x10 y80 w700 h350 +Grid", ["时间", "操作类型", "元素名称", "脚本代码", "详细描述"])

    ; 状态栏
    statusBar := monitorGui.Add("Text", "x10 y440", "热键: F9-开始/停止 | F10-统计 | F11-清空 | F12-退出 | 支持窗口拖拽调整大小")
    statusBar.Name := "StatusBar"

    ; 设置列宽 - 优化显示
    logListView.ModifyCol(1, 70)   ; 时间
    logListView.ModifyCol(2, 90)   ; 操作类型
    logListView.ModifyCol(3, 140)  ; 元素名称
    logListView.ModifyCol(4, 200)  ; 脚本代码
    logListView.ModifyCol(5, 180)  ; 详细描述

    ; 显示界面 - 增大默认尺寸
    monitorGui.Show("w720 h470")

    ; 设置窗口事件
    monitorGui.OnEvent("Close", (*) => ExitMonitor())
    monitorGui.OnEvent("Size", OnGuiResize)
}

; 窗口大小调整事件处理
OnGuiResize(GuiObj, MinMax, Width, Height) {
    global logListView

    if MinMax = -1  ; 最小化时不处理
        return

    ; 调整ListView大小以适应窗口
    if logListView {
        newWidth := Width - 30
        newHeight := Height - 130

        ; 确保最小尺寸
        if newWidth < 400
            newWidth := 400
        if newHeight < 200
            newHeight := 200

        logListView.Move(, , newWidth, newHeight)

        ; 调整状态栏位置
        try {
            statusBarControl := GuiObj["StatusBar"]
            statusBarControl.Move(10, Height - 30)
        } catch {
            ; 忽略错误
        }
    }
}

; 导出详细日志
ExportDetailedLog() {
    global operationCount, logFile

    if operationCount = 0 {
        MsgBox("暂无监控数据可导出")
        return
    }

    ; 生成详细报告
    reportFile := "CoppeliaSim_Detailed_Report_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"

    try {
        report := "=== CoppeliaSim 详细操作报告 ===`n"
        report .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        report .= "总操作数量: " . operationCount . "`n`n"

        ; 读取原始日志并格式化
        if FileExist(logFile) {
            logContent := FileRead(logFile, "UTF-8")
            report .= "=== 详细操作记录 ===`n"
            report .= logContent
        }

        FileAppend(report, reportFile, "UTF-8")
        MsgBox("详细报告已导出到: " . reportFile)

    } catch Error as e {
        MsgBox("导出失败: " . e.Message)
    }
}

; 开始/停止监控
ToggleMonitoring() {
    global monitoringActive, coppliaElement, eventHandlers

    if !monitoringActive {
        ; 开始监控
        try {
            StartEventMonitoring()
            monitoringActive := true
            UpdateStatus("监控中", 0x00FF00)
            AddLog("开始监控 CoppeliaSim 操作")
        } catch Error as e {
            MsgBox("启动监控失败: " . e.Message)
        }
    } else {
        ; 停止监控
        StopEventMonitoring()
        monitoringActive := false
        UpdateStatus("已停止", 0xFF0000)
        AddLog("停止监控")
    }
}

; 开始事件监控
StartEventMonitoring() {
    global coppliaElement, eventHandlers

    try {
        ; 简化监控方式，主要使用定时器监控
        ; UIA 事件处理在某些情况下可能不稳定，所以我们使用更可靠的方法

        ; 启动监控 - 降低频率减少资源占用
        SetTimer(MonitorMouseClicks, 50)      ; 鼠标监控保持较高频率
        SetTimer(MonitorKeyboard, 100)       ; 键盘监控适中频率
        SetTimer(MonitorWindowChanges, 2000)  ; 窗口变化监控降低频率

        AddLog("事件监控已启动")

    } catch Error as e {
        throw Error("启动事件监控失败: " . e.Message)
    }
}

; 停止事件监控
StopEventMonitoring() {
    global eventHandlers

    try {
        ; 停止所有定时器
        SetTimer(MonitorMouseClicks, 0)
        SetTimer(MonitorKeyboard, 0)
        SetTimer(MonitorWindowChanges, 0)

        AddLog("事件监控已停止")

    } catch Error as e {
        AddLog("停止监控时出错: " . e.Message)
    }
}

; 监控窗口变化（优化版 - 减少无意义的记录）
MonitorWindowChanges() {
    global coppliaElement, monitoringActive

    if !monitoringActive
        return

    static lastTitle := ""
    static lastCheckTime := 0

    ; 降低检查频率，避免过度监控
    currentTime := A_TickCount
    if (currentTime - lastCheckTime < 1000) ; 至少间隔1秒
        return
    lastCheckTime := currentTime

    try {
        ; 检查 CoppeliaSim 窗口标题变化（只关注仿真状态变化）
        if WinExist("ahk_exe coppeliasim.exe") {
            currentTitle := WinGetTitle("ahk_exe coppeliasim.exe")
            if currentTitle != lastTitle && lastTitle != "" {
                ; 只记录重要的状态变化
                if InStr(currentTitle, "SIMULATION STOPPED") && !InStr(lastTitle, "SIMULATION STOPPED") {
                    LogDetailedOperation("状态变化", "仿真停止", "sim.stopSimulation()", "仿真状态从运行变为停止")
                } else if !InStr(currentTitle, "SIMULATION STOPPED") && InStr(lastTitle, "SIMULATION STOPPED") {
                    LogDetailedOperation("状态变化", "仿真开始", "sim.startSimulation()", "仿真状态从停止变为运行")
                }
                ; 移除其他不重要的标题变化记录
            }
            lastTitle := currentTitle
        }
    } catch {
        ; 忽略错误
    }
}

; 鼠标点击监控（优化版 - 只监控实际点击事件）
MonitorMouseClicks() {
    global coppliaElement, monitoringActive

    if !monitoringActive
        return

    static lastClickTime := 0
    static lastClickX := 0
    static lastClickY := 0
    static wasPressed := false

    ; 检测鼠标按下和释放事件，只在释放时记录（真正的点击）
    isPressed := GetKeyState("LButton", "P")

    if !wasPressed && isPressed {
        ; 鼠标刚按下，记录位置但不记录操作
        MouseGetPos(&lastClickX, &lastClickY)
        wasPressed := true
    } else if wasPressed && !isPressed {
        ; 鼠标刚释放，这是一个完整的点击操作
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 300) { ; 防止重复检测
            MouseGetPos(&x, &y, &winId)

            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                ; 检查鼠标移动距离，如果移动太多可能是拖拽而不是点击
                moveDistance := Sqrt(Abs(x - lastClickX)**2 + Abs(y - lastClickY)**2)
                if moveDistance < 10 { ; 移动距离小于10像素才认为是点击
                    try {
                        ; 获取点击位置的元素 - 改进版本，获取更准确的元素信息
                        clickedElement := UIA.ElementFromPoint(x, y)
                        if clickedElement {
                            ; 获取更详细的元素信息
                            elementInfo := GetEnhancedElementInfo(clickedElement, x, y)

                            ; 过滤掉一些不重要的元素
                            if ShouldRecordElement(elementInfo.Name, elementInfo.Type, elementInfo.ClassName) {
                                ; 分析操作并生成脚本建议 - 使用增强的分析
                                scriptCode := AnalyzeElementForScriptEnhanced(clickedElement, elementInfo)
                                detailedInfo := BuildEnhancedDetailedInfo(elementInfo, x, y)

                                ; 记录详细操作信息
                                LogDetailedOperation("鼠标点击", elementInfo.DisplayName, scriptCode, detailedInfo)
                            }
                        }
                    } catch {
                        ; 忽略获取元素失败的情况
                    }
                }
            }

            lastClickTime := currentTime
        }
        wasPressed := false
    }
}

; 键盘监控
MonitorKeyboard() {
    global monitoringActive

    if !monitoringActive
        return

    static lastKeyTime := 0
    static monitoredKeys := ["Enter", "Space", "Tab", "Escape", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"]

    ; 检查特殊按键
    for key in monitoredKeys {
        if GetKeyState(key, "P") {
            currentTime := A_TickCount
            if (currentTime - lastKeyTime > 300) { ; 防止重复检测
                ; 检查当前窗口是否为 CoppeliaSim
                if WinActive("ahk_exe coppeliasim.exe") {
                    scriptCode := AnalyzeKeyScript(key)
                    detailedInfo := "在 CoppeliaSim 中按下 " . key . " 键"
                    LogDetailedOperation("按键操作", key, scriptCode, detailedInfo)
                    lastKeyTime := currentTime
                }
            }
        }
    }
}

; 分析操作行为
AnalyzeOperation(element, elementName) {
    try {
        ; 分析仿真相关操作
        if InStr(elementName, "simulation") || InStr(elementName, "仿真") {
            if InStr(elementName, "Start") || InStr(elementName, "start") {
                LogOperation("仿真控制", "开始仿真", "用户启动了仿真 | 可能的脚本: sim.startSimulation()")
            } else if InStr(elementName, "Stop") || InStr(elementName, "stop") {
                LogOperation("仿真控制", "停止仿真", "用户停止了仿真 | 可能的脚本: sim.stopSimulation()")
            } else if InStr(elementName, "Suspend") || InStr(elementName, "pause") {
                LogOperation("仿真控制", "暂停仿真", "用户暂停了仿真 | 可能的脚本: sim.pauseSimulation()")
            }
        }

        ; 分析菜单操作
        if element.Type = UIA.Type.MenuItem {
            AnalyzeMenuOperation(elementName)
        }

        ; 分析工具栏操作
        if element.Type = UIA.Type.Button || element.Type = UIA.Type.CheckBox {
            AnalyzeToolbarOperation(elementName)
        }

    } catch {
        ; 忽略分析错误
    }
}

; 分析菜单操作
AnalyzeMenuOperation(menuName) {
    switch menuName {
        case "File":
            LogOperation("菜单操作", "文件菜单", "可能操作: 新建、打开、保存场景 | 脚本: sim.loadScene(), sim.saveScene()")
        case "Edit":
            LogOperation("菜单操作", "编辑菜单", "可能操作: 撤销、重做、复制、粘贴 | 脚本: sim.copyPasteObjects()")
        case "Add":
            LogOperation("菜单操作", "添加菜单", "可能操作: 添加对象、形状、传感器 | 脚本: sim.createPrimitiveShape()")
        case "Simulation":
            LogOperation("菜单操作", "仿真菜单", "可能操作: 仿真设置、参数配置 | 脚本: sim.setSimulationTimeStep()")
        case "Tools":
            LogOperation("菜单操作", "工具菜单", "可能操作: 计算模块、脚本编辑 | 脚本: sim.openModule()")
        case "Plugins":
            LogOperation("菜单操作", "插件菜单", "可能操作: 加载插件、插件设置 | 脚本: sim.loadPlugin()")
        default:
            LogOperation("菜单操作", menuName, "用户点击了菜单项")
    }
}

; 分析工具栏操作
AnalyzeToolbarOperation(toolName) {
    if InStr(toolName, "Camera") {
        LogOperation("视图控制", toolName, "相机操作 | 脚本: sim.setCameraMatrix()")
    } else if InStr(toolName, "selection") || InStr(toolName, "Selection") {
        LogOperation("对象操作", toolName, "选择操作 | 脚本: sim.getObjectSelection()")
    } else if InStr(toolName, "shift") || InStr(toolName, "rotate") {
        LogOperation("对象操作", toolName, "对象变换 | 脚本: sim.setObjectPosition(), sim.setObjectOrientation()")
    } else if InStr(toolName, "Undo") {
        LogOperation("编辑操作", "撤销", "撤销上一步操作")
    } else if InStr(toolName, "Redo") {
        LogOperation("编辑操作", "重做", "重做操作")
    }
}

; 分析点击操作
AnalyzeClickOperation(element, elementName) {
    try {
        ; 获取元素的更多信息
        elementClass := element.ClassName ? element.ClassName : ""
        elementValue := element.Value ? element.Value : ""

        ; 分析特定的点击操作
        if InStr(elementClass, "QToolButton") {
            LogOperation("工具按钮", elementName, "工具栏按钮点击 | 类: " . elementClass)
        } else if InStr(elementClass, "QComboBox") {
            LogOperation("下拉选择", elementName, "下拉框操作 | 当前值: " . elementValue)
        } else if InStr(elementClass, "QListWidget") {
            LogOperation("列表操作", elementName, "模型列表操作")
        } else if InStr(elementClass, "QTreeWidget") {
            LogOperation("树形控件", elementName, "场景层次结构操作")
        }

    } catch {
        ; 忽略分析错误
    }
}

; 记录详细操作日志（新版本，支持5列显示）
LogDetailedOperation(operationType, elementName, scriptCode, detailedInfo) {
    global logListView, operationCount, logFile

    operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")

    ; 添加到界面列表（5列：时间、操作类型、元素名称、脚本代码、详细描述）
    if logListView {
        logListView.Add(, currentTime, operationType, elementName, scriptCode, detailedInfo)
        ; 自动滚动到最新项
        logListView.Modify(logListView.GetCount(), "Vis")
    }

    ; 写入详细日志文件 - 使用 UTF-8 编码，添加错误处理
    logEntry := "时间: " . currentTime . "`n"
    logEntry .= "操作: " . operationType . "`n"
    logEntry .= "元素: " . elementName . "`n"
    logEntry .= "脚本: " . scriptCode . "`n"
    logEntry .= "详情: " . detailedInfo . "`n"
    logEntry .= "---`n"

    try {
        FileAppend(logEntry, logFile, "UTF-8")
    } catch {
        ; 忽略文件写入错误，不影响监控功能
    }

    ; 更新计数显示
    UpdateOperationCount()
}

; 兼容旧版本的LogOperation函数
LogOperation(operationType, elementName, details) {
    LogDetailedOperation(operationType, elementName, "-- 操作记录", details)
}

; 分析元素对应的脚本代码
AnalyzeElementForScript(element, elementName, elementType) {
    try {
        ; 根据元素名称和类型分析对应的脚本
        if InStr(elementName, "Start") && InStr(elementName, "simulation") {
            return "sim.startSimulation()"
        } else if InStr(elementName, "Stop") && InStr(elementName, "simulation") {
            return "sim.stopSimulation()"
        } else if InStr(elementName, "Suspend") && InStr(elementName, "simulation") {
            return "sim.pauseSimulation(true)"
        } else if InStr(elementName, "Camera") {
            if InStr(elementName, "pan") {
                return "-- 相机平移操作"
            } else if InStr(elementName, "rotate") {
                return "-- 相机旋转操作"
            } else if InStr(elementName, "shift") {
                return "-- 相机移动操作"
            } else {
                return "sim.setCameraMatrix(cameraHandle, matrix)"
            }
        } else if InStr(elementName, "selection") || InStr(elementName, "Selection") {
            return "sim.getObjectSelection()"
        } else if InStr(elementName, "shift") || InStr(elementName, "rotate") {
            return "sim.setObjectPosition() / sim.setObjectOrientation()"
        } else if element.Type = UIA.Type.MenuItem {
            return AnalyzeMenuItemScript(elementName)
        } else if element.Type = UIA.Type.Button {
            return AnalyzeButtonScript(elementName)
        } else if element.Type = UIA.Type.CheckBox {
            return AnalyzeCheckBoxScript(elementName)
        } else if InStr(elementName, "Undo") {
            return "-- 撤销操作"
        } else if InStr(elementName, "Redo") {
            return "-- 重做操作"
        } else {
            return "-- " . elementType . "操作"
        }
    } catch {
        return "-- 未知操作"
    }
}

; 分析菜单项脚本
AnalyzeMenuItemScript(menuName) {
    switch menuName {
        case "File":
            return "sim.loadScene() / sim.saveScene()"
        case "Edit":
            return "-- 编辑操作"
        case "Add":
            return "sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1,1,1})"
        case "Simulation":
            return "sim.setSimulationTimeStep(0.05)"
        case "Tools":
            return "-- 工具操作"
        case "Plugins":
            return "sim.loadPlugin('pluginName')"
        default:
            return "-- 菜单: " . menuName
    }
}

; 分析按钮脚本
AnalyzeButtonScript(buttonName) {
    if InStr(buttonName, "Fit to view") {
        return "-- 适应视图操作"
    } else if InStr(buttonName, "Assemble") {
        return "-- 组装/拆卸操作"
    } else if InStr(buttonName, "Undo") {
        return "-- 撤销操作"
    } else if InStr(buttonName, "Redo") {
        return "-- 重做操作"
    } else {
        return "-- 按钮: " . buttonName
    }
}

; 分析复选框脚本
AnalyzeCheckBoxScript(checkboxName) {
    if InStr(checkboxName, "Camera") {
        return "-- 相机控制模式切换"
    } else if InStr(checkboxName, "selection") {
        return "-- 选择模式切换"
    } else if InStr(checkboxName, "real-time") {
        return "sim.setRealTimeSimulation(true/false)"
    } else if InStr(checkboxName, "visualization") {
        return "-- 可视化切换"
    } else {
        return "-- 复选框: " . checkboxName
    }
}

; 构建详细信息
BuildDetailedInfo(elementName, elementType, elementClass, elementValue, x, y) {
    detailParts := []

    ; 添加类型信息
    if elementType != "未知类型" && elementType != "" {
        detailParts.Push("类型:" . elementType)
    }

    ; 添加类名信息
    if elementClass != "" {
        detailParts.Push("类:" . elementClass)
    }

    ; 添加值信息
    if elementValue != "" {
        detailParts.Push("值:" . elementValue)
    }

    ; 添加位置信息
    detailParts.Push("位置:(" . x . "," . y . ")")

    ; 组合详细信息
    return StrJoin(detailParts, " | ")
}

; 分析按键对应的脚本
AnalyzeKeyScript(key) {
    switch key {
        case "Enter":
            return "-- 确认操作"
        case "Space":
            return "-- 空格键操作（可能是播放/暂停）"
        case "Tab":
            return "-- 切换焦点"
        case "Escape":
            return "-- 取消/退出操作"
        case "F1":
            return "-- 帮助"
        case "F2":
            return "-- 重命名操作"
        case "F3":
            return "-- 查找下一个"
        case "F4":
            return "-- 可能的窗口操作"
        case "F5":
            return "-- 刷新/重新加载"
        case "F6":
            return "-- 切换面板"
        case "F7":
            return "-- 可能的调试操作"
        case "F8":
            return "-- 可能的步进操作"
        case "F9":
            return "-- 可能的断点操作"
        case "F10":
            return "-- 可能的菜单操作"
        case "F11":
            return "-- 可能的全屏切换"
        case "F12":
            return "-- 可能的开发者工具"
        default:
            return "-- 按键: " . key
    }
}

; 判断是否应该记录该元素（过滤不重要的元素）
ShouldRecordElement(elementName, elementType, elementClass) {
    ; 过滤掉一些不重要或频繁触发的元素

    ; 跳过无名称且类型未知的元素
    if elementName = "[无名称]" && elementType = "未知类型"
        return false

    ; 跳过一些系统级的元素
    if InStr(elementClass, "QSplitter") || InStr(elementClass, "QScrollBar")
        return false

    ; 跳过一些装饰性元素
    if elementType = "分隔符" || elementType = "Separator"
        return false

    ; 跳过空白区域
    if elementName = "" && elementType = "窗格"
        return false

    ; 只记录有意义的交互元素
    meaningfulTypes := ["按钮", "Button", "复选框", "CheckBox", "菜单项", "MenuItem",
                       "工具栏", "ToolBar", "列表项", "ListItem", "树项", "TreeItem",
                       "下拉框", "ComboBox", "编辑框", "Edit"]

    for type in meaningfulTypes {
        if InStr(elementType, type)
            return true
    }

    ; 如果有明确的名称，也记录
    if elementName != "[无名称]" && elementName != ""
        return true

    return false
}

; 字符串连接辅助函数
StrJoin(array, separator) {
    result := ""
    for i, item in array {
        if i > 1
            result .= separator
        result .= item
    }
    return result
}

; 添加日志条目（简化版本）
AddLog(message) {
    LogOperation("系统", "监控系统", message)
}

; 更新状态显示
UpdateStatus(status, color) {
    global monitorGui

    if monitorGui {
        try {
            statusControl := monitorGui["StatusText"]
            statusControl.Text := status
            statusControl.Opt("c" . Format("0x{:06X}", color))
        } catch {
            ; 忽略更新错误
        }
    }
}

; 更新操作计数
UpdateOperationCount() {
    global monitorGui, operationCount

    if monitorGui {
        try {
            countControl := monitorGui["CountText"]
            countControl.Text := operationCount
        } catch {
            ; 忽略更新错误
        }
    }
}

; 显示统计信息
ShowStatistics() {
    global operationCount, logFile

    ; 读取日志文件获取详细统计
    try {
        logContent := FileRead(logFile, "UTF-8")
        lines := StrSplit(logContent, "`n")

        ; 统计不同类型的操作
        stats := Map()
        stats["鼠标点击"] := 0
        stats["按键操作"] := 0
        stats["仿真控制"] := 0
        stats["菜单操作"] := 0
        stats["工具按钮"] := 0
        stats["其他操作"] := 0

        for line in lines {
            if InStr(line, "鼠标点击")
                stats["鼠标点击"]++
            else if InStr(line, "按键操作")
                stats["按键操作"]++
            else if InStr(line, "仿真控制")
                stats["仿真控制"]++
            else if InStr(line, "菜单操作")
                stats["菜单操作"]++
            else if InStr(line, "工具按钮")
                stats["工具按钮"]++
            else if InStr(line, "|")
                stats["其他操作"]++
        }

        ; 构建统计信息
        statsText := "=== CoppeliaSim 操作统计 ===`n`n"
        statsText .= "总操作次数: " . operationCount . "`n`n"
        statsText .= "操作类型分布:`n"

        for type, count in stats {
            if count > 0 {
                percentage := Round((count / operationCount) * 100, 1)
                statsText .= "• " . type . ": " . count . " 次 (" . percentage . "%)`n"
            }
        }

        statsText .= "`n日志文件: " . logFile

        MsgBox(statsText, "操作统计")

    } catch Error as e {
        MsgBox("获取统计信息失败: " . e.Message)
    }
}

; 清空日志
ClearLog() {
    global logListView, operationCount, logFile

    result := MsgBox("确定要清空所有监控日志吗？", "确认清空", "YesNo")
    if result = "Yes" {
        ; 清空界面列表
        if logListView {
            logListView.Delete()
        }

        ; 重置计数
        operationCount := 0
        UpdateOperationCount()

        ; 清空日志文件
        try {
            FileDelete(logFile)
        } catch {
            ; 忽略删除失败
        }

        try {
            FileAppend("=== CoppeliaSim 操作监控日志 ===`n", logFile, "UTF-8")
            FileAppend("日志清空时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", logFile, "UTF-8")
        } catch {
            ; 忽略文件操作错误
        }

        AddLog("监控日志已清空")
    }
}

; 退出监控程序
ExitMonitor() {
    global monitoringActive

    if monitoringActive {
        StopEventMonitoring()
    }

    AddLog("监控系统退出")
    ExitApp
}

; ===== 增强的元素分析函数 =====

; 获取增强的元素信息
GetEnhancedElementInfo(element, x, y) {
    elementInfo := {}

    try {
        ; 基本信息
        elementInfo.Name := element.Name ? element.Name : "[无名称]"
        elementInfo.Type := element.LocalizedType ? element.LocalizedType : "未知类型"
        elementInfo.ClassName := element.ClassName ? element.ClassName : ""
        elementInfo.Value := element.Value ? element.Value : ""
        elementInfo.AutomationId := element.AutomationId ? element.AutomationId : ""
        elementInfo.HelpText := element.HelpText ? element.HelpText : ""

        ; 尝试获取更准确的显示名称
        elementInfo.DisplayName := GetBetterElementName(element, elementInfo)

        ; 获取父元素信息以提供上下文
        try {
            parentElement := element.GetParentElement()
            if parentElement {
                elementInfo.ParentName := parentElement.Name ? parentElement.Name : ""
                elementInfo.ParentType := parentElement.LocalizedType ? parentElement.LocalizedType : ""
                elementInfo.ParentClass := parentElement.ClassName ? parentElement.ClassName : ""
            }
        } catch {
            elementInfo.ParentName := ""
            elementInfo.ParentType := ""
            elementInfo.ParentClass := ""
        }

        ; 获取控件模式信息
        elementInfo.Patterns := GetElementPatterns(element)

        ; 位置信息
        elementInfo.X := x
        elementInfo.Y := y

        ; 尝试获取边界矩形
        try {
            boundingRect := element.BoundingRectangle
            elementInfo.BoundingRect := boundingRect
        } catch {
            elementInfo.BoundingRect := ""
        }

    } catch Error as e {
        ; 如果获取信息失败，返回基本信息
        elementInfo.Name := "[获取失败]"
        elementInfo.DisplayName := "[获取失败]"
        elementInfo.Type := "未知"
        elementInfo.ClassName := ""
        elementInfo.Value := ""
        elementInfo.AutomationId := ""
        elementInfo.HelpText := ""
        elementInfo.ParentName := ""
        elementInfo.ParentType := ""
        elementInfo.ParentClass := ""
        elementInfo.Patterns := []
        elementInfo.X := x
        elementInfo.Y := y
        elementInfo.BoundingRect := ""
    }

    return elementInfo
}

; 获取更好的元素名称
GetBetterElementName(element, elementInfo) {
    ; 如果有明确的名称，使用它
    if elementInfo.Name != "[无名称]" && elementInfo.Name != "" {
        return elementInfo.Name
    }

    ; 如果有AutomationId，使用它
    if elementInfo.AutomationId != "" {
        return "[ID:" . elementInfo.AutomationId . "]"
    }

    ; 如果有帮助文本，使用它
    if elementInfo.HelpText != "" {
        return "[帮助:" . elementInfo.HelpText . "]"
    }

    ; 如果有值，使用它
    if elementInfo.Value != "" {
        return "[值:" . elementInfo.Value . "]"
    }

    ; 根据类名推断
    if InStr(elementInfo.ClassName, "QToolButton") {
        return "[工具按钮]"
    } else if InStr(elementInfo.ClassName, "QComboBox") {
        return "[下拉框]"
    } else if InStr(elementInfo.ClassName, "QListWidget") {
        return "[列表控件]"
    } else if InStr(elementInfo.ClassName, "QTreeWidget") {
        return "[树形控件]"
    } else if InStr(elementInfo.ClassName, "QMenuBar") {
        return "[菜单栏]"
    } else if InStr(elementInfo.ClassName, "QMenu") {
        return "[菜单]"
    } else if InStr(elementInfo.ClassName, "QAction") {
        return "[菜单项]"
    } else if InStr(elementInfo.ClassName, "QPushButton") {
        return "[按钮]"
    } else if InStr(elementInfo.ClassName, "QCheckBox") {
        return "[复选框]"
    } else if InStr(elementInfo.ClassName, "QLineEdit") {
        return "[文本框]"
    } else if InStr(elementInfo.ClassName, "QLabel") {
        return "[标签]"
    }

    ; 根据类型推断
    if elementInfo.Type != "未知类型" && elementInfo.Type != "" {
        return "[" . elementInfo.Type . "]"
    }

    ; 最后使用类名
    if elementInfo.ClassName != "" {
        return "[类:" . elementInfo.ClassName . "]"
    }

    return "[未知元素]"
}

; 获取元素支持的模式
GetElementPatterns(element) {
    patterns := []

    try {
        ; 检查常见的控件模式
        patternList := [
            {name: "Invoke", id: UIA.Pattern.Invoke},
            {name: "Selection", id: UIA.Pattern.Selection},
            {name: "SelectionItem", id: UIA.Pattern.SelectionItem},
            {name: "Toggle", id: UIA.Pattern.Toggle},
            {name: "Value", id: UIA.Pattern.Value},
            {name: "Text", id: UIA.Pattern.Text},
            {name: "ScrollItem", id: UIA.Pattern.ScrollItem},
            {name: "ExpandCollapse", id: UIA.Pattern.ExpandCollapse}
        ]

        for pattern in patternList {
            try {
                if element.GetCurrentPattern(pattern.id) {
                    patterns.Push(pattern.name)
                }
            } catch {
                ; 忽略不支持的模式
            }
        }
    } catch {
        ; 忽略模式检查错误
    }

    return patterns
}

; 增强的脚本分析函数
AnalyzeElementForScriptEnhanced(element, elementInfo) {
    try {
        ; 基于更详细的信息分析脚本
        displayName := elementInfo.DisplayName
        className := elementInfo.ClassName
        parentName := elementInfo.ParentName
        patterns := elementInfo.Patterns
        automationId := elementInfo.AutomationId

        ; 仿真控制相关
        if InStr(displayName, "Start") || InStr(displayName, "开始") || InStr(displayName, "启动") {
            if InStr(displayName, "simulation") || InStr(displayName, "仿真") || InStr(parentName, "simulation") {
                return "sim.startSimulation() -- 开始仿真"
            }
        }

        if InStr(displayName, "Stop") || InStr(displayName, "停止") {
            if InStr(displayName, "simulation") || InStr(displayName, "仿真") || InStr(parentName, "simulation") {
                return "sim.stopSimulation() -- 停止仿真"
            }
        }

        if InStr(displayName, "Pause") || InStr(displayName, "Suspend") || InStr(displayName, "暂停") {
            if InStr(displayName, "simulation") || InStr(displayName, "仿真") || InStr(parentName, "simulation") {
                return "sim.pauseSimulation(true) -- 暂停仿真"
            }
        }

        ; 基于AutomationId的精确匹配
        if automationId != "" {
            scriptFromId := AnalyzeByAutomationId(automationId)
            if scriptFromId != ""
                return scriptFromId
        }

        ; 基于类名的分析
        if InStr(className, "QToolButton") {
            return AnalyzeToolButtonScript(displayName, parentName, elementInfo)
        } else if InStr(className, "QComboBox") {
            return AnalyzeComboBoxScript(displayName, elementInfo)
        } else if InStr(className, "QListWidget") {
            return AnalyzeListWidgetScript(displayName, elementInfo)
        } else if InStr(className, "QTreeWidget") {
            return AnalyzeTreeWidgetScript(displayName, elementInfo)
        } else if InStr(className, "QMenuBar") || InStr(className, "QMenu") {
            return AnalyzeMenuScript(displayName, elementInfo)
        } else if InStr(className, "QPushButton") {
            return AnalyzePushButtonScript(displayName, elementInfo)
        } else if InStr(className, "QAction") {
            return AnalyzeActionScript(displayName, elementInfo)
        }

        ; 基于控件模式的分析
        for pattern in patterns {
            switch pattern {
                case "Invoke":
                    return "-- 可调用操作: " . displayName
                case "Toggle":
                    return "-- 切换操作: " . displayName
                case "Selection":
                    return "sim.getObjectSelection() -- 选择操作"
                case "Value":
                    return "-- 值操作: " . displayName
            }
        }

        ; 通用分析
        return AnalyzeGenericElement(displayName, elementInfo)

    } catch Error as e {
        return "-- 分析失败: " . displayName
    }
}

; 基于AutomationId分析脚本
AnalyzeByAutomationId(automationId) {
    ; CoppeliaSim特定的AutomationId映射
    idMappings := Map(
        "startSimulation", "sim.startSimulation() -- 开始仿真",
        "stopSimulation", "sim.stopSimulation() -- 停止仿真",
        "pauseSimulation", "sim.pauseSimulation(true) -- 暂停仿真",
        "cameraShift", "-- 相机平移模式",
        "cameraRotate", "-- 相机旋转模式",
        "cameraZoom", "-- 相机缩放模式",
        "objectShift", "sim.setObjectPosition(objectHandle, position) -- 对象移动",
        "objectRotate", "sim.setObjectOrientation(objectHandle, orientation) -- 对象旋转",
        "fitToView", "-- 适应视图",
        "realTimeSimulation", "sim.setRealTimeSimulation(enabled) -- 实时仿真切换"
    )

    if idMappings.Has(automationId) {
        return idMappings[automationId]
    }

    return ""
}

; 分析工具按钮脚本
AnalyzeToolButtonScript(displayName, parentName, elementInfo) {
    ; 相机控制
    if InStr(displayName, "Camera") || InStr(displayName, "相机") {
        if InStr(displayName, "shift") || InStr(displayName, "平移") {
            return "-- 相机平移模式 (鼠标拖拽平移视图)"
        } else if InStr(displayName, "rotate") || InStr(displayName, "旋转") {
            return "-- 相机旋转模式 (鼠标拖拽旋转视图)"
        } else if InStr(displayName, "zoom") || InStr(displayName, "缩放") {
            return "-- 相机缩放模式 (鼠标滚轮缩放)"
        } else {
            return "sim.setCameraMatrix(cameraHandle, matrix) -- 相机操作"
        }
    }

    ; 对象操作
    if InStr(displayName, "Object") || InStr(displayName, "对象") {
        if InStr(displayName, "shift") || InStr(displayName, "移动") {
            return "sim.setObjectPosition(objectHandle, {x, y, z}) -- 对象位置"
        } else if InStr(displayName, "rotate") || InStr(displayName, "旋转") {
            return "sim.setObjectOrientation(objectHandle, {alpha, beta, gamma}) -- 对象方向"
        }
    }

    ; 选择工具
    if InStr(displayName, "selection") || InStr(displayName, "选择") {
        return "sim.getObjectSelection() -- 获取选中对象"
    }

    ; 仿真控制
    if InStr(displayName, "simulation") || InStr(displayName, "仿真") {
        if InStr(displayName, "real-time") || InStr(displayName, "实时") {
            return "sim.setRealTimeSimulation(true/false) -- 实时仿真切换"
        }
    }

    return "-- 工具按钮: " . displayName
}

; 分析下拉框脚本
AnalyzeComboBoxScript(displayName, elementInfo) {
    value := elementInfo.Value

    if InStr(displayName, "simulation") || InStr(displayName, "仿真") {
        return "sim.setSimulationTimeStep(" . value . ") -- 仿真时间步长"
    } else if InStr(displayName, "engine") || InStr(displayName, "引擎") {
        return "sim.setEngineFloatParameter() -- 物理引擎参数"
    } else if InStr(displayName, "render") || InStr(displayName, "渲染") {
        return "-- 渲染设置: " . value
    }

    return "-- 下拉选择: " . displayName . " = " . value
}

; 分析列表控件脚本
AnalyzeListWidgetScript(displayName, elementInfo) {
    if InStr(displayName, "model") || InStr(displayName, "模型") {
        return "sim.loadModel('modelPath') -- 加载模型"
    } else if InStr(displayName, "scene") || InStr(displayName, "场景") {
        return "sim.loadScene('scenePath') -- 加载场景"
    } else if InStr(displayName, "object") || InStr(displayName, "对象") {
        return "sim.getObjectHandle('objectName') -- 获取对象句柄"
    }

    return "-- 列表选择: " . displayName
}

; 分析树形控件脚本
AnalyzeTreeWidgetScript(displayName, elementInfo) {
    if InStr(displayName, "Scene hierarchy") || InStr(displayName, "场景层次") {
        return "sim.getObjectsInTree(sim.handle_scene) -- 场景层次结构"
    } else if InStr(displayName, "Collection") || InStr(displayName, "集合") {
        return "sim.createCollection(0) -- 创建集合"
    }

    return "-- 树形结构操作: " . displayName
}

; 分析菜单脚本
AnalyzeMenuScript(displayName, elementInfo) {
    ; 文件菜单
    if InStr(displayName, "File") || InStr(displayName, "文件") {
        if InStr(displayName, "New") || InStr(displayName, "新建") {
            return "sim.closeScene() + sim.loadScene('') -- 新建场景"
        } else if InStr(displayName, "Open") || InStr(displayName, "打开") {
            return "sim.loadScene('scenePath') -- 打开场景"
        } else if InStr(displayName, "Save") || InStr(displayName, "保存") {
            return "sim.saveScene('scenePath') -- 保存场景"
        } else if InStr(displayName, "Import") || InStr(displayName, "导入") {
            return "sim.importShape() / sim.importMesh() -- 导入模型"
        } else if InStr(displayName, "Export") || InStr(displayName, "导出") {
            return "sim.exportShape() / sim.exportMesh() -- 导出模型"
        }
    }

    ; 编辑菜单
    if InStr(displayName, "Edit") || InStr(displayName, "编辑") {
        if InStr(displayName, "Undo") || InStr(displayName, "撤销") {
            return "-- 撤销操作"
        } else if InStr(displayName, "Redo") || InStr(displayName, "重做") {
            return "-- 重做操作"
        } else if InStr(displayName, "Copy") || InStr(displayName, "复制") {
            return "sim.copyPasteObjects(objectHandles, 1) -- 复制对象"
        } else if InStr(displayName, "Paste") || InStr(displayName, "粘贴") {
            return "sim.copyPasteObjects(objectHandles, 0) -- 粘贴对象"
        } else if InStr(displayName, "Delete") || InStr(displayName, "删除") {
            return "sim.removeObjects(objectHandles) -- 删除对象"
        }
    }

    ; 添加菜单
    if InStr(displayName, "Add") || InStr(displayName, "添加") {
        if InStr(displayName, "Primitive shape") || InStr(displayName, "基本形状") {
            return "sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1,1,1}) -- 创建基本形状"
        } else if InStr(displayName, "Joint") || InStr(displayName, "关节") {
            return "sim.createJoint(sim.joint_revolute_subtype, sim.jointmode_kinematic) -- 创建关节"
        } else if InStr(displayName, "Sensor") || InStr(displayName, "传感器") {
            return "sim.createProximitySensor() -- 创建传感器"
        } else if InStr(displayName, "Light") || InStr(displayName, "光源") {
            return "sim.createLight(sim.light_omnidirectional_subtype) -- 创建光源"
        }
    }

    ; 仿真菜单
    if InStr(displayName, "Simulation") || InStr(displayName, "仿真") {
        if InStr(displayName, "Settings") || InStr(displayName, "设置") {
            return "sim.setSimulationTimeStep(0.05) -- 仿真设置"
        } else if InStr(displayName, "Parameters") || InStr(displayName, "参数") {
            return "sim.setEngineFloatParameter() -- 引擎参数"
        }
    }

    return "-- 菜单操作: " . displayName
}

; 分析按钮脚本
AnalyzePushButtonScript(displayName, elementInfo) {
    if InStr(displayName, "OK") || InStr(displayName, "确定") {
        return "-- 确认操作"
    } else if InStr(displayName, "Cancel") || InStr(displayName, "取消") {
        return "-- 取消操作"
    } else if InStr(displayName, "Apply") || InStr(displayName, "应用") {
        return "-- 应用设置"
    } else if InStr(displayName, "Browse") || InStr(displayName, "浏览") {
        return "-- 文件浏览"
    }

    return "-- 按钮操作: " . displayName
}

; 分析动作脚本
AnalyzeActionScript(displayName, elementInfo) {
    ; 这通常是菜单项或工具栏动作
    return AnalyzeMenuScript(displayName, elementInfo)
}

; 通用元素分析
AnalyzeGenericElement(displayName, elementInfo) {
    ; 基于显示名称的通用分析
    if InStr(displayName, "simulation") || InStr(displayName, "仿真") {
        return "-- 仿真相关操作: " . displayName
    } else if InStr(displayName, "camera") || InStr(displayName, "相机") {
        return "-- 相机相关操作: " . displayName
    } else if InStr(displayName, "object") || InStr(displayName, "对象") {
        return "-- 对象相关操作: " . displayName
    } else if InStr(displayName, "scene") || InStr(displayName, "场景") {
        return "-- 场景相关操作: " . displayName
    } else if InStr(displayName, "model") || InStr(displayName, "模型") {
        return "-- 模型相关操作: " . displayName
    }

    return "-- 操作: " . displayName
}

; 构建增强的详细信息
BuildEnhancedDetailedInfo(elementInfo, x, y) {
    detailParts := []

    ; 添加显示名称（如果与原名称不同）
    if elementInfo.DisplayName != elementInfo.Name && elementInfo.Name != "[无名称]" {
        detailParts.Push("原名:" . elementInfo.Name)
    }

    ; 添加类型信息
    if elementInfo.Type != "未知类型" && elementInfo.Type != "" {
        detailParts.Push("类型:" . elementInfo.Type)
    }

    ; 添加类名信息（简化显示）
    if elementInfo.ClassName != "" {
        className := elementInfo.ClassName
        ; 简化Qt类名显示
        if InStr(className, "Q") = 1 {
            className := SubStr(className, 2)  ; 去掉Q前缀
        }
        detailParts.Push("类:" . className)
    }

    ; 添加AutomationId
    if elementInfo.AutomationId != "" {
        detailParts.Push("ID:" . elementInfo.AutomationId)
    }

    ; 添加值信息
    if elementInfo.Value != "" {
        detailParts.Push("值:" . elementInfo.Value)
    }

    ; 添加父元素信息（如果有用）
    if elementInfo.ParentName != "" && elementInfo.ParentName != elementInfo.Name {
        detailParts.Push("父级:" . elementInfo.ParentName)
    }

    ; 添加支持的模式
    if elementInfo.Patterns.Length > 0 {
        patternStr := ""
        for pattern in elementInfo.Patterns {
            if patternStr != ""
                patternStr .= ","
            patternStr .= pattern
        }
        detailParts.Push("模式:" . patternStr)
    }

    ; 添加位置信息
    detailParts.Push("位置:(" . x . "," . y . ")")

    ; 添加边界信息（如果可用）
    if elementInfo.BoundingRect != "" {
        try {
            rect := elementInfo.BoundingRect
            detailParts.Push("区域:" . rect.left . "," . rect.top . "," . rect.right . "," . rect.bottom)
        } catch {
            ; 忽略边界信息错误
        }
    }

    ; 组合详细信息
    return StrJoin(detailParts, " | ")
}
