﻿=== CoppeliaSim UI 结构（UTF-8 编码）===
生成时间: 2025-07-30 17:58:31
编码格式: UTF-8
说明: 此文件使用 UTF-8 编码，中文字符应能正确显示
===================================================

Type: 50032 (Window) Name: "coppeliasim" LocalizedType: "窗口" ClassName: "CMainWindow"
1: Type: 50037 (TitleBar) Value: "CoppeliaSim Edu - New file - rendering: 1 ms (7.9 fps) - SIMULATION STOPPED" LocalizedType: "标题栏" AutomationId: "TitleBar"
1,1: Type: 50010 (MenuBar) Name: "系统" LocalizedType: "菜单栏" AutomationId: "SystemMenuBar"
1,1,1: Type: 50011 (MenuItem) Name: "系统" LocalizedType: "菜单项目"
1,2: Type: 50000 (Button) Name: "还原" LocalizedType: "按钮" AutomationId: "Minimize-Restore"
1,3: Type: 50000 (Button) Name: "最大化" LocalizedType: "按钮" AutomationId: "Maximize-Restore"
1,4: Type: 50000 (Button) Name: "关闭" LocalizedType: "按钮" AutomationId: "Close"
2: Type: 50025 (Custom) LocalizedType: "自定义" ClassName: "QSplitter"
2,1: Type: 50025 (Custom) LocalizedType: "自定义" ClassName: "QSplitter"
2,1,1: Type: 50026 (Group) LocalizedType: "组" ClassName: "QWidget"
2,1,1,1: Type: 50004 (Edit) Value: "[sandboxScript:info]   Simulator launched, welcome!
[CoppeliaSim:info]   Loading model (C:/Program Files/CoppeliaRobotics/CoppeliaSimEdu/models/robots/non-mobile/ABB IRB 140.ttm).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.00.01 (rev 0)
[CoppeliaSim:info]   Model loaded.
[CoppeliaSim:info]   Deleting selection...
[CoppeliaSim:info]   done.
[CoppeliaSim:info]   Loading scene...
[CoppeliaSim:info]   Default scene was set-up.
[CoppeliaSim:info]   Loading scene (D:/Coppliasimtest/coppliasim-python.ttt).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.01.00 (rev 1)
[CoppeliaSim:info]   Scene opened.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   Simulation suspended.
[sandboxScript:info]   Simulation resumed.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   Simulation suspended.
[sandboxScript:info]   Simulation resumed.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[CoppeliaSim:info]   Loading model (C:/Program Files/CoppeliaRobotics/CoppeliaSimEdu/models/robots/non-mobile/ABB IRB 140.ttm).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.00.01 (rev 0)
[CoppeliaSim:info]   Model loaded.
[CoppeliaSim:info]   Deleting selection...
[CoppeliaSim:info]   done.
[CoppeliaSim:info]   Loading model (C:/Program Files/CoppeliaRobotics/CoppeliaSimEdu/models/robots/non-mobile/ABB IRB 140.ttm).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.00.01 (rev 0)
[CoppeliaSim:info]   Model loaded.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   Simulation suspended.
[sandboxScript:info]   Simulation resumed.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped." LocalizedType: "编辑" ClassName: "CStatusBar"
2,1,1,1,1: Type: 50026 (Group) LocalizedType: "组" ClassName: "QWidget"
2,1,2: Type: 50026 (Group) LocalizedType: "组" ClassName: "QWidget"
2,1,2,1: Type: 50018 (Tab) Name: "new scene" LocalizedType: "选项卡" ClassName: "QTabBar"
2,1,2,1,1: Type: 50019 (TabItem) Name: "new scene" LocalizedType: "选项卡项目"
2,1,2,1,2: Type: 50019 (TabItem) Name: "coppliasim-python" LocalizedType: "选项卡项目"
2,1,2,2: Type: 50025 (Custom) LocalizedType: "自定义" ClassName: "QSplitter"
2,1,2,2,1: Type: 50026 (Group) LocalizedType: "组" ClassName: "COpenglWidget"
2,1,3: Type: 50027 (Thumb) LocalizedType: "缩略图" ClassName: "QSplitterHandle"
2,2: Type: 50025 (Custom) LocalizedType: "自定义" ClassName: "QSplitter"
2,2,1: Type: 50008 (List) LocalizedType: "列表" ClassName: "CModelListWidget"
2,2,1,1: Type: 50007 (ListItem) Name: "7 DoF manipulator.ttm" LocalizedType: "列表项目"
2,2,1,2: Type: 50007 (ListItem) Name: "ABB IRB 140.ttm" LocalizedType: "列表项目"
2,2,2: Type: 50023 (Tree) LocalizedType: "树" ClassName: "CModelFolderWidget"
2,2,2,1: Type: 50034 (Header) Name: "Model browser" LocalizedType: "标头"
2,2,2,2: Type: 50024 (TreeItem) Name: "components" LocalizedType: "树项目"
2,2,2,3: Type: 50024 (TreeItem) Name: "equipment" LocalizedType: "树项目"
2,2,2,4: Type: 50024 (TreeItem) Name: "examples" LocalizedType: "树项目"
2,2,2,5: Type: 50024 (TreeItem) Name: "furniture" LocalizedType: "树项目"
2,2,2,6: Type: 50024 (TreeItem) Name: "household" LocalizedType: "树项目"
2,2,2,7: Type: 50024 (TreeItem) Name: "infrastructure" LocalizedType: "树项目"
2,2,2,8: Type: 50024 (TreeItem) Name: "nature" LocalizedType: "树项目"
2,2,2,9: Type: 50024 (TreeItem) Name: "office items" LocalizedType: "树项目"
2,2,2,10: Type: 50024 (TreeItem) Name: "other" LocalizedType: "树项目"
2,2,2,11: Type: 50024 (TreeItem) Name: "people" LocalizedType: "树项目"
2,2,2,12: Type: 50024 (TreeItem) Name: "robots" LocalizedType: "树项目"
2,2,2,13: Type: 50024 (TreeItem) Name: "mobile" LocalizedType: "树项目"
2,2,2,14: Type: 50024 (TreeItem) Name: "non-mobile" LocalizedType: "树项目"
2,2,2,15: Type: 50024 (TreeItem) Name: "tools" LocalizedType: "树项目"
2,2,2,16: Type: 50024 (TreeItem) Name: "vehicles" LocalizedType: "树项目"
2,2,3: Type: 50027 (Thumb) LocalizedType: "缩略图" ClassName: "QSplitterHandle"
2,3: Type: 50027 (Thumb) LocalizedType: "缩略图" ClassName: "QSplitterHandle"
3: Type: 50010 (MenuBar) LocalizedType: "菜单栏" ClassName: "QMenuBar"
3,1: Type: 50011 (MenuItem) Name: "File" LocalizedType: "菜单项目" ClassName: "QAction"
3,2: Type: 50011 (MenuItem) Name: "Edit" LocalizedType: "菜单项目" ClassName: "QAction"
3,3: Type: 50011 (MenuItem) Name: "Add" LocalizedType: "菜单项目" ClassName: "QAction"
3,4: Type: 50011 (MenuItem) Name: "Simulation" LocalizedType: "菜单项目" ClassName: "QAction"
3,5: Type: 50011 (MenuItem) Name: "Tools" LocalizedType: "菜单项目" ClassName: "QAction"
3,6: Type: 50011 (MenuItem) Name: "Plugins" LocalizedType: "菜单项目" ClassName: "QAction"
3,7: Type: 50011 (MenuItem) Name: "Add-ons" LocalizedType: "菜单项目" ClassName: "QAction"
3,8: Type: 50011 (MenuItem) Name: "Scenes" LocalizedType: "菜单项目" ClassName: "QAction"
3,9: Type: 50011 (MenuItem) Name: "Help" LocalizedType: "菜单项目" ClassName: "QAction"
4: Type: 50021 (ToolBar) Name: "Navigation" LocalizedType: "工具栏" ClassName: "QToolBar"
4,1: Type: 50002 (CheckBox) LocalizedType: "复选框" ClassName: "QToolBarExtension"
4,2: Type: 50002 (CheckBox) Name: "Camera pan" LocalizedType: "复选框" ClassName: "QToolButton"
4,3: Type: 50002 (CheckBox) Name: "Camera rotate" LocalizedType: "复选框" ClassName: "QToolButton"
4,4: Type: 50002 (CheckBox) Name: "Camera shift" LocalizedType: "复选框" ClassName: "QToolButton"
4,5: Type: 50002 (CheckBox) Name: "Camera opening angle/view size" LocalizedType: "复选框" ClassName: "QToolButton"
4,6: Type: 50000 (Button) Name: "Fit to view" LocalizedType: "按钮" ClassName: "QToolButton"
4,7: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
4,8: Type: 50002 (CheckBox) Name: "Click selection" LocalizedType: "复选框" ClassName: "QToolButton"
4,9: Type: 50002 (CheckBox) Name: "Object/item shift" LocalizedType: "复选框" ClassName: "QToolButton"
4,10: Type: 50002 (CheckBox) Name: "Object/item rotate" LocalizedType: "复选框" ClassName: "QToolButton"
4,11: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
4,12: Type: 50000 (Button) Name: "Assemble / Disassemble" LocalizedType: "按钮" ClassName: "QToolButton"
4,13: Type: 50000 (Button) Name: "Transfer DNA to siblings" LocalizedType: "按钮" ClassName: "QToolButton"
4,14: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
4,15: Type: 50000 (Button) Name: "Undo" LocalizedType: "按钮" ClassName: "QToolButton"
4,16: Type: 50000 (Button) Name: "Redo" LocalizedType: "按钮" ClassName: "QToolButton"
4,17: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
4,18: Type: 50002 (CheckBox) Name: "Visualize and verify dynamic content (during simulation only)" LocalizedType: "复选框" ClassName: "QToolButton"
4,19: Type: 50003 (ComboBox) Value: "Bullet 2.78" LocalizedType: "组合框" ClassName: "QComboBox"
4,19,1: Type: 50008 (List) LocalizedType: "列表" ClassName: "QComboBoxListView"
4,19,1,1: Type: 50007 (ListItem) Name: "Bullet 2.78" LocalizedType: "列表项目"
4,19,1,2: Type: 50007 (ListItem) Name: "Bullet 2.83" LocalizedType: "列表项目"
4,19,1,3: Type: 50007 (ListItem) Name: "ODE" LocalizedType: "列表项目"
4,19,1,4: Type: 50007 (ListItem) Name: "Vortex" LocalizedType: "列表项目"
4,19,1,5: Type: 50007 (ListItem) Name: "Newton" LocalizedType: "列表项目"
4,20: Type: 50003 (ComboBox) Value: "Accurate (default)" LocalizedType: "组合框" ClassName: "QComboBox"
4,20,1: Type: 50008 (List) LocalizedType: "列表" ClassName: "QComboBoxListView"
4,20,1,1: Type: 50007 (ListItem) Name: "Very accurate" LocalizedType: "列表项目"
4,20,1,2: Type: 50007 (ListItem) Name: "Accurate (default)" LocalizedType: "列表项目"
4,20,1,3: Type: 50007 (ListItem) Name: "Fast" LocalizedType: "列表项目"
4,20,1,4: Type: 50007 (ListItem) Name: "Very fast" LocalizedType: "列表项目"
4,20,1,5: Type: 50007 (ListItem) Name: "Customized" LocalizedType: "列表项目"
4,21: Type: 50003 (ComboBox) Value: "dt=50 ms (default)" LocalizedType: "组合框" ClassName: "QComboBox"
4,21,1: Type: 50008 (List) LocalizedType: "列表" ClassName: "QComboBoxListView"
4,21,1,1: Type: 50007 (ListItem) Name: "dt=200 ms" LocalizedType: "列表项目"
4,21,1,2: Type: 50007 (ListItem) Name: "dt=100 ms" LocalizedType: "列表项目"
4,21,1,3: Type: 50007 (ListItem) Name: "dt=50 ms (default)" LocalizedType: "列表项目"
4,21,1,4: Type: 50007 (ListItem) Name: "dt=25 ms" LocalizedType: "列表项目"
4,21,1,5: Type: 50007 (ListItem) Name: "dt=10 ms" LocalizedType: "列表项目"
4,21,1,6: Type: 50007 (ListItem) Name: "dt=50.0 ms (custom)" LocalizedType: "列表项目"
4,22: Type: 50002 (CheckBox) Name: "Start/resume simulation" LocalizedType: "复选框" ClassName: "QToolButton"
4,23: Type: 50002 (CheckBox) Name: "Suspend simulation" LocalizedType: "复选框" ClassName: "QToolButton"
4,24: Type: 50000 (Button) Name: "Stop simulation" LocalizedType: "按钮" ClassName: "QToolButton"
5: Type: 50021 (ToolBar) Name: "Tools" LocalizedType: "工具栏" ClassName: "QToolBar"
5,1: Type: 50002 (CheckBox) Name: "Simulation settings" LocalizedType: "复选框" ClassName: "QToolButton"
5,2: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
5,3: Type: 50002 (CheckBox) Name: "Scene object properties" LocalizedType: "复选框" ClassName: "QToolButton"
5,4: Type: 50002 (CheckBox) Name: "Calculation module properties" LocalizedType: "复选框" ClassName: "QToolButton"
5,5: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
5,6: Type: 50002 (CheckBox) Name: "Collections" LocalizedType: "复选框" ClassName: "QToolButton"
5,7: Type: 50002 (CheckBox) Name: "Scripts" LocalizedType: "复选框" ClassName: "QToolButton"
5,8: Type: 50002 (CheckBox) Name: "Toggle shape edit mode (make sure to have a single shape object selected)" LocalizedType: "复选框" ClassName: "QToolButton"
5,9: Type: 50002 (CheckBox) Name: "Toggle path edit mode (make sure to have a single path object selected)" LocalizedType: "复选框" ClassName: "QToolButton"
5,10: Type: 50026 (Group) LocalizedType: "组" ClassName: "QToolBarSeparator"
5,11: Type: 50002 (CheckBox) Name: "Selection" LocalizedType: "复选框" ClassName: "QToolButton"
5,12: Type: 50002 (CheckBox) Name: "Model browser" LocalizedType: "复选框" ClassName: "QToolButton"
5,13: Type: 50002 (CheckBox) Name: "Scene hierarchy" LocalizedType: "复选框" ClassName: "QToolButton"
5,14: Type: 50002 (CheckBox) Name: "Layers" LocalizedType: "复选框" ClassName: "QToolButton"
5,15: Type: 50002 (CheckBox) Name: "Video recorder" LocalizedType: "复选框" ClassName: "QToolButton"
5,16: Type: 50002 (CheckBox) Name: "User settings" LocalizedType: "复选框" ClassName: "QToolButton"