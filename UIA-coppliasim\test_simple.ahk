#Requires AutoHotkey v2

; 最简单的测试脚本
MsgBox("AutoHotkey v2 工作正常！`n版本: " . A_AhkVersion)

; 测试基本功能
result := MsgBox("是否继续测试 UIA 库？", "测试", "YesNo")

if result = "Yes" {
    ; 检查 UIA 库文件
    if FileExist("..\UIA-v2-1.1.0\Lib\UIA.ahk") {
        MsgBox("UIA 库文件存在")
        
        ; 尝试加载 UIA 库
        try {
            #include ..\UIA-v2-1.1.0\Lib\UIA.ahk
            MsgBox("UIA 库加载成功！")
        } catch Error as e {
            MsgBox("UIA 库加载失败: " . e.Message)
        }
    } else {
        MsgBox("UIA 库文件不存在`n路径: ..\UIA-v2-1.1.0\Lib\UIA.ahk")
    }
}

ExitApp
