﻿#include Lib\UIA.ahk

#SingleInstance force
F12::reload

;查看当前鼠标下的元素信息和它父亲
F10:: {
    el := UIA.ElementFromPoint()
    el.Highlight(2000)
    elFather := el.GetParentElement()
    aRect := elFather.GetBoundingRectangle()
    msgbox("Father element info: " elFather.Dump())
    msgbox("Bounding rectangle: " Format("{1}", aRect))
}

F9:: {
    if WinActive("ahk_class Chrome_WidgetWin_1") { ;谷歌浏览器
        if WinActive("百度一下") { ;百度模拟搜索
            idA := WinGetID()
            ;设置搜索内容
            elEdit := UIA.GetFocusedElement()
            elEdit.GetCurrentPattern("Value").SetValue("火冷-博客园")
            sleep(2000)
            ;点击搜索按钮
            el := UIA.ElementFromHandle(idA).FindElement({Type:"Button", Name:"百度一下"})
            el.GetCurrentPattern("Invoke").Invoke()
            return
        } else { ;谷歌获取网址
            chromeEl := UIA.ElementFromHandle(ControlGetHwnd("Chrome_RenderWidgetHostHWND1", "A"))
            msgbox("Chrome element info: " chromeEl.Dump())
        }
    } else if WinActive("ahk_class WeChatMainWndForPC") { ;微信右键消息内容并点击多选
        if !WinExist("ahk_class CMenuWnd")
            send("{RButton}")
        WinWait("ahk_class CMenuWnd")
        try
            UIA.ElementFromHandle(WinGetID()).FindElement({Type:"MenuItem", Name:"多选"}).Click()
    }
}
