# CoppeliaSim 改进版监控系统使用说明

## 🎯 改进内容

### 1. 界面改进
- ✅ **支持窗口大小调整**：可以从左下角拖拽调整窗口大小
- ✅ **最小尺寸限制**：防止窗口过小影响使用
- ✅ **自适应布局**：ListView 和状态栏会随窗口大小自动调整

### 2. 监控内容改进
- ✅ **5列详细显示**：时间、操作类型、元素名称、脚本代码、详细描述
- ✅ **脚本代码建议**：每个操作都会显示对应的 Lua 脚本代码
- ✅ **详细描述信息**：包含元素类型、类名、值、位置等详细信息
- ✅ **智能操作分析**：根据不同元素类型提供相应的脚本建议

### 3. 新增功能
- ✅ **导出详细日志**：可以导出包含时间戳的详细报告
- ✅ **改进的日志格式**：更易读的日志文件格式
- ✅ **网格显示**：ListView 添加网格线，更清晰

## 📊 监控显示内容对比

### 改进前：
```
时间     | 操作类型  | 元素名称        | 详细信息
16:30:24 | 鼠标点击  | [无名称]        | 位置: (245,93) | 类型: 复选框
```

### 改进后：
```
时间     | 操作类型  | 元素名称              | 脚本代码              | 详细描述
16:30:24 | 鼠标点击  | Start/resume simulation | sim.startSimulation() | 类型:复选框 | 类:QToolButton | 位置:(245,93)
```

## 🔧 具体改进功能

### 1. 窗口大小调整
- **拖拽调整**：从窗口边缘或角落拖拽调整大小
- **最小尺寸**：640x480 像素，确保内容可见
- **自动适应**：ListView 和状态栏自动调整位置和大小

### 2. 详细的脚本代码分析

#### 仿真控制操作：
- 开始仿真 → `sim.startSimulation()`
- 停止仿真 → `sim.stopSimulation()`
- 暂停仿真 → `sim.pauseSimulation(true)`

#### 相机操作：
- 相机平移 → `-- 相机平移操作`
- 相机旋转 → `-- 相机旋转操作`
- 相机矩阵 → `sim.setCameraMatrix(cameraHandle, matrix)`

#### 对象操作：
- 选择对象 → `sim.getObjectSelection()`
- 移动对象 → `sim.setObjectPosition() / sim.setObjectOrientation()`

#### 菜单操作：
- 文件菜单 → `sim.loadScene() / sim.saveScene()`
- 添加菜单 → `sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1,1,1})`
- 插件菜单 → `sim.loadPlugin('pluginName')`

#### 按键操作：
- Enter → `-- 确认操作`
- Space → `-- 空格键操作（可能是播放/暂停）`
- F5 → `-- 刷新/重新加载`

### 3. 详细描述信息

每个操作都会显示：
- **元素类型**：按钮、复选框、菜单项等
- **元素类名**：QToolButton、QComboBox 等 Qt 类名
- **元素值**：如果有值会显示
- **点击位置**：屏幕坐标

### 4. 导出功能

点击"导出日志"按钮会生成：
- **文件名**：`CoppeliaSim_Detailed_Report_yyyyMMdd_HHmmss.txt`
- **内容包含**：
  - 报告头信息（时间、操作数量）
  - 详细的操作记录
  - 格式化的日志内容

## 🚀 使用方法

### 1. 启动监控
```
1. 确保 CoppeliaSim 正在运行
2. 运行改进后的 Monitor_coppliasim_simple.ahk
3. 点击"开始监控"或按 F9
```

### 2. 调整窗口大小
```
1. 将鼠标移到窗口边缘或角落
2. 当鼠标变成双向箭头时拖拽
3. ListView 会自动调整大小
```

### 3. 查看详细信息
```
1. 在 CoppeliaSim 中进行各种操作
2. 观察监控界面的5列显示
3. 查看"脚本代码"列了解对应的 Lua 代码
4. 查看"详细描述"列了解元素详情
```

### 4. 导出报告
```
1. 点击"导出日志"按钮
2. 选择保存位置
3. 查看生成的详细报告文件
```

## 📋 生成的日志格式

### 界面显示格式：
```
时间      操作类型    元素名称                脚本代码                详细描述
16:30:24  鼠标点击    Start/resume simulation  sim.startSimulation()   类型:复选框 | 类:QToolButton | 位置:(245,93)
16:30:25  状态变化    仿真开始                sim.startSimulation()   仿真状态从停止变为运行 | 标题: CoppeliaSim...
16:30:30  按键操作    Space                   -- 空格键操作           在 CoppeliaSim 中按下 Space 键
```

### 日志文件格式：
```
时间: 16:30:24
操作: 鼠标点击
元素: Start/resume simulation
脚本: sim.startSimulation()
详情: 类型:复选框 | 类:QToolButton | 位置:(245,93)
---
```

## 🎯 实际应用示例

### 示例1：监控仿真操作
1. 在 CoppeliaSim 中点击"开始仿真"
2. 监控显示：
   - 操作类型：鼠标点击
   - 元素名称：Start/resume simulation
   - 脚本代码：sim.startSimulation()
   - 详细描述：类型:复选框 | 类:QToolButton | 位置:(245,93)

### 示例2：监控相机操作
1. 在 CoppeliaSim 中拖拽相机视角
2. 监控显示：
   - 操作类型：鼠标点击
   - 元素名称：Camera rotate
   - 脚本代码：-- 相机旋转操作
   - 详细描述：类型:复选框 | 类:QToolButton | 位置:(156,78)

### 示例3：监控菜单操作
1. 点击"Add"菜单
2. 监控显示：
   - 操作类型：鼠标点击
   - 元素名称：Add
   - 脚本代码：sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1,1,1})
   - 详细描述：类型:菜单项 | 类:QAction | 位置:(200,150)

## 🔍 故障排除

### 1. 窗口调整不生效
- 确保鼠标在窗口边缘
- 尝试重启监控程序

### 2. 脚本代码显示不准确
- 这是正常现象，脚本代码是基于元素名称的智能推测
- 可以作为参考，具体实现需要根据实际需求调整

### 3. 详细信息显示不完整
- 某些元素可能没有完整的属性信息
- 这取决于 CoppeliaSim 的 UI 实现

## 📈 后续改进计划

1. **更智能的脚本分析**：基于操作序列生成更完整的脚本
2. **操作录制回放**：记录操作序列并生成可执行脚本
3. **自定义脚本模板**：允许用户自定义操作到脚本的映射
4. **实时代码预览**：在监控界面显示实时生成的完整脚本

---

**版本**：v2.0 改进版  
**更新时间**：2024年  
**主要改进**：窗口调整、详细显示、脚本分析、导出功能
