@echo off
chcp 65001
echo 正在启动 CoppeliaSim 监控系统...
echo.

REM 检查 AutoHotkey 是否存在
if not exist "C:\AutoHotkey\v2\AutoHotkey64.exe" (
    echo 错误：找不到 AutoHotkey v2
    echo 请确保 AutoHotkey v2 已正确安装在 C:\AutoHotkey\v2\
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "Monitor_coppliasim_simple.ahk" (
    echo 错误：找不到监控脚本文件
    echo 请确保在正确的目录中运行此批处理文件
    pause
    exit /b 1
)

REM 检查 UIA 库是否存在
if not exist "..\UIA-v2-1.1.0\Lib\UIA.ahk" (
    echo 错误：找不到 UIA 库文件
    echo 请确保 UIA-v2-1.1.0 文件夹在正确位置
    pause
    exit /b 1
)

echo 所有文件检查通过，正在启动监控系统...
echo.

REM 启动监控脚本
"C:\AutoHotkey\v2\AutoHotkey64.exe" /ErrorStdOut=utf-8 "Monitor_coppliasim_simple.ahk"

if errorlevel 1 (
    echo.
    echo 监控系统启动失败
    echo 错误代码: %errorlevel%
    pause
) else (
    echo.
    echo 监控系统已退出
)

pause
